45aff94da1f6461299810ee7eae27dd82442b8d3|2025-06-27 16:05:50 +0300|<PERSON><PERSON><PERSON>|Add setup project action for Node.js and environment configuration
ce6e90b728e4da69de0fbe8070f7eb3d45370ae1|2025-06-25 16:22:22 +0300|<PERSON><PERSON><PERSON>|Update CI workflow to remove unit tests step from status checks
da1709aa085bfb28ff952b97ea2c0b14ea7bed97|2025-06-25 16:19:51 +0300|<PERSON><PERSON><PERSON>|Remove unit tests step from CI workflow
bba3fe6c30f4d9f76a429b8b910b4d1c3c2a82ac|2025-06-25 16:17:10 +0300|Pav<PERSON>|Remove Prettier check from CI workflow
880ff6142307e2d5f910ee2e0657277045b04c29|2025-06-25 16:11:20 +0300|<PERSON><PERSON><PERSON>|Refactor components for improved readability and consistency
d9635f8f25dbae7f72285641b52df67a7c462b2b|2025-06-25 15:47:56 +0300|Pavlo Shepitchak|Enhance deployment workflow with manual trigger and CI status checks
87f5e9eee90e4395e64a040c359c920d8947e7b2|2025-06-24 16:09:50 +0300|Pavlo Shepitchak|Add GitHub Actions for Expo setup and CI workflow enhancements
5ee2f032eb08c67e1c3b91b00997e444509e0f66|2025-06-24 16:06:37 +0300|Pavlo Shepitchak|Remove Sentry error capture button from index.tsx
a5ff98206e9c8a1bf97abbe36d78b33f6dfa5496|2025-06-24 15:59:09 +0300|Pavlo Shepitchak|Integrate Sentry for error tracking and performance monitoring
bef0912d5c7a4e108a1b51eda40f3d8f132c1bec|2025-06-20 14:12:36 +0300|Pavlo Shepitchak|Enhance expo-router configuration for async routes in development
e52eef04d18126728c34191d8630d14b3585f8d1|2025-06-20 12:22:27 +0300|Pavlo Shepitchak|Improve Playwright CI workflow debugging and error handling
a44c4b30a1643f5b97b665b14e67672aa79c06a7|2025-06-20 12:11:13 +0300|Pavlo Shepitchak|Improve CI test server reliability
ed2c5f3bcbf80bd6fb883997b96ce419e2b92fd3|2025-06-20 12:06:54 +0300|Pavlo Shepitchak|Use dev server instead of production build for tests
57e2b58baf0a485e449ff569ae0092b2195b9d2c|2025-06-20 12:02:27 +0300|Pavlo Shepitchak|Use dev server instead of production build for tests
d3ad9e133176fd1046a4ba93dc690e89fa44abb3|2025-06-20 11:49:02 +0300|Pavlo Shepitchak|Add SSH debug capabilities to Playwright workflow
9f0169ccc57cb8a58adf146ae5e11e340fdad612|2025-06-20 11:44:50 +0300|Pavlo Shepitchak|Simplify web server setup and update CI workflow
8b3e3c2f73769270af4286ddfcd01789d8b82d18|2025-06-19 18:14:26 +0300|Pavlo Shepitchak|chore: update expo to version 53.0.12 and react-native to version 0.79.4
bdfdd5dcc29793d945768526598cc1b4bdc70830|2025-06-19 18:12:09 +0300|Pavlo Shepitchak|chore: update expo to version 53.0.12 and react-native to version 0.79.4
c0c4919b1dbb88d5a55a4af836e2c7dc9915d62f|2025-06-19 18:07:11 +0300|Pavlo Shepitchak|chore: update Node.js version to 22.14.0 in Playwright configuration
5d761a4afa797f463da9190e40e231347c3c7ccf|2025-06-19 17:49:14 +0300|Pavlo Shepitchak|chore: update Node.js version to 22.14.0 in Playwright configuration
6f7a42254864e8e83df85cf46020ba6a39e338d5|2025-06-19 17:37:27 +0300|Pavlo Shepitchak|chore: change Playwright test trigger from pull_request to push for main and dev branches
5c0595593b50c41b86c5609246e90a1e933a691c|2025-06-19 17:34:52 +0300|Pavlo Shepitchak|chore: update Playwright Docker image to v1.53.0 for improved functionality
fcf531dd39c178a0db13f720f31247e6b5d56359|2025-06-19 17:33:59 +0300|Pavlo Shepitchak|chore: update Playwright Docker image to v1.53.0 for improved functionality
0bd48e5b30a4ffe20832901a4103bdb8f6eb6e77|2025-06-19 17:31:45 +0300|Pavlo Shepitchak|chore: remove unused environment variable from deploy configuration
f21bb424c9a52669bd43733c32902892d5d5b731|2025-06-19 17:22:19 +0300|Pavlo Shepitchak|chore: remove unused environment variable from deploy configuration
83dc6d4346daae33af8a47d3a78f5beffc8ff836|2025-06-18 15:33:08 +0300|Pavlo Shepitchak|test: add Playwright test for course interaction in takeACourse
9b9a332d53cb4db43ce562e4e9560646ebc04005|2025-06-18 14:59:08 +0300|Pavlo Shepitchak|test: add Playwright test for course navigation functionality
55b9a5afb6886296af6504ade594fc4701f68bb6|2025-06-13 16:11:49 +0300|Pavlo Shepitchak|refactor: clean up code formatting and improve readability in multiple components
67896cf6b9373c47fd2e64a5ca49c8a29ca6236b|2025-06-13 15:57:57 +0300|Pavlo Shepitchak|refactor: update skills data formatting to use a standard skills set
0f1c6e5049dd93cd0e2e4c397dbb8c27fb020870|2025-06-13 13:34:48 +0300|Pavlo Shepitchak|refactor: update skills data formatting to use a standard skills set
c7db668dcf597ffaf41f4e1014be089d04d2c2e1|2025-06-13 13:07:42 +0300|Pavlo Shepitchak|feat: implement NavigationButton component for improved navigation
084309c102b42bf99aa94026c52d7c59d74ce51f|2025-06-12 14:56:05 +0300|Pavlo Shepitchak|Merge pull request #41
40d38139b3e87bac5b780aca89da87b54b3cd786|2025-06-11 12:36:20 +0300|Pavlo Shepitchak|Merge pull request #39 from shepitchakpavlo/storybook
b64ca9c841deea75ebeaea8bc0bb7a40b485836e|2025-06-11 12:16:30 +0300|Pavlo Shepitchak|fix: remove extraneous characters from package.json
2fae35a2f59996601715f40018bbad145d4646a1|2025-06-11 12:15:23 +0300|Pavlo Shepitchak|refactor: remove unused styles from Button component
6f7e4cde9b7d3819f57f268ba3597e893d89a51d|2025-06-11 12:14:12 +0300|Pavlo Shepitchak|refactor: remove unnecessary styles from Header component
52cf9ebac63ace021f6e6de159ea3c72a849920d|2025-06-11 12:12:24 +0300|Pavlo Shepitchak|feat: add Chromatic integration for visual testing in Storybook
1e77b736efdaf6c10279c5420d0b106a00759e15|2025-06-11 12:06:36 +0300|Pavlo Shepitchak|feat: add Storybook configuration and initial stories for components
dc56ee4e74a96fb9aae7513c4ca779accef1fba1|2025-06-10 16:45:08 +0300|Pavlo Shepitchak|chore: update typescript version to 5.8.3
5704db870cc69e660ebea39b1f1d8136e88b100d|2025-06-10 16:38:11 +0300|Pavlo Shepitchak|refactor: simplify guest condition check in index.tsx
f02b0da39d976cb4bbf016a52ab55f26ba160433|2025-06-10 16:37:23 +0300|Pavlo Shepitchak|refactor: clean up imports and improve logout handling
d331d953649d9ebb62f96da019d0fef097aa919c|2025-06-10 16:33:32 +0300|Pavlo Shepitchak|Merge remote-tracking branch 'origin/dev' into dev
f4da8f899eae61831595b831fd1b58b912f201f1|2025-06-10 16:32:57 +0300|Pavlo Shepitchak|fix: enforce onComplete prop as required in CourseSlideProps interface
794abad42845b088595e4fe82ed48f6ffa56583f|2025-06-10 16:18:26 +0300|Pavlo Shepitchak|Merge pull request #38 from shepitchakpavlo/dependabot/npm_and_yarn/typescript-eslint/eslint-plugin-8.33.1
a1089b3499583e745cc229cb88b92e152ff3afda|2025-06-10 13:50:50 +0300|Pavlo Shepitchak|feat(slides): include slide ID in the response data structure
79efbdd147e32befc52aeb4cd1353f5995a47a66|2025-06-10 13:47:45 +0300|Pavlo Shepitchak|feat(slides): enhance slide retrieval to include type and title in response
472add593e9899e675f9304ca250e4b3a97d57c6|2025-06-09 16:41:56 +0300|Pavlo Shepitchak|feat(auth): implement guest user detection and logout functionality
dfeffa3272ff07ff764c76bc7a191cebe9ffa1f7|2025-06-09 13:19:20 +0000|dependabot[bot]|chore(deps-dev): bump @typescript-eslint/eslint-plugin
09c039180eb6988088339df3bb35a245e9abc817|2025-06-09 15:50:56 +0300|Pavlo Shepitchak|feat(auth): implement guest user detection and logout functionality
597142a7bebf6f042b3599d9da81d9f99b7d08cc|2025-06-06 15:12:13 +0300|Pavlo Shepitchak|fix(chat): remove uppercase styling from chat header title
515821b3f0b2b2f14643d1439314ad988def451b|2025-06-06 15:03:40 +0300|Pavlo Shepitchak|feat(courses): add lesson 2 to psychology in elite sales course
bb6e65c4fea07ba41a01645168f7fdd73b31c939|2025-06-05 15:26:11 +0300|Pavlo Shepitchak|feat(courses): add lesson 2 to psychology in elite sales course
3689b382ab4ace601f9908c62e0bf106259d30b0|2025-06-05 15:22:55 +0300|Pavlo Shepitchak|docs: add database structure documentation
38d96f430c1eb555c6b21db3050ed948b574424d|2025-06-05 15:16:41 +0300|Pavlo Shepitchak|docs: add database structure documentation
72978263f995783c07eb6f253896531cb4dbf3b0|2025-06-05 15:04:37 +0300|Pavlo Shepitchak|refactor: consolidate course lessons into single file
a2766a1cfa115d14299cb21b363d57b3a8dbd1da|2025-06-05 15:02:44 +0300|Pavlo Shepitchak|``` feat: add course content for psychology in elite sales
ba4c1c56e6b9300e78c037ad2e61a32b6bcd2cf8|2025-06-05 13:57:27 +0300|Pavlo Shepitchak|feat(courses): add Heygen video IDs for Open Development course
01efb19c286e29fc4bfb294226153f7229d53530|2025-06-05 13:09:43 +0300|Pavlo Shepitchak|feat(courses): add Heygen video IDs for Open Development course
7e6bfc3afba3f184dcced4417932a8981cebcf1a|2025-06-05 12:54:11 +0300|Pavlo Shepitchak|feat(courses): add video configuration for Open Development course
ebccf78a08b8deb47be3985d18537c355d3491e7|2025-06-05 12:38:28 +0300|Pavlo Shepitchak|chore: add Supabase project configuration to .windsurfrules
fbbeba4f5d24f515a82b32460859d7dec6acf749|2025-06-05 12:38:11 +0300|Pavlo Shepitchak|docs: add workflow for creating video config from course markdown
8dc982c274b97215718439e7dfe679d5f96f3c80|2025-06-03 16:06:33 +0300|Pavlo Shepitchak|refactor: improve AI chat with slide-based prompts and API refactoring
09511bec7ae5c0ba2e2352ca203c0a337a2f5b62|2025-06-03 16:03:39 +0300|Pavlo Shepitchak|refactor: improve AI chat with slide-based prompts and API refactoring
47988bba4920c679518ca73925b6b70b8bfc3bc5|2025-05-30 16:02:58 +0300|Pavlo Shepitchak|feat(database): add AI prompts support for course slides
d48536ce0d05109a59e0c20c40bb5add584b6bbb|2025-05-30 15:47:43 +0300|Pavlo Shepitchak|refactor(database): simplify course slides architecture
0fbf5f741c250428e99ce4f18496e71443bf17cd|2025-05-30 15:47:40 +0300|Pavlo Shepitchak|refactor(database): simplify course slides architecture
5a5d15ddfe2c6aed67f2313bec1699bad26af4d6|2025-05-30 15:28:49 +0300|Pavlo Shepitchak|feat(course): optimize slide loading with separate slide IDs endpoint
bb247fe1363970b5638cd040e8292bd0e10e98d3|2025-05-30 15:10:06 +0300|Pavlo Shepitchak|feat(CourseScreen): refactor to use TanStack Query for data fetching
fd040c512e66e6ed2dac8e5bdec0f36d1d19bb8b|2025-05-30 14:54:25 +0300|Pavlo Shepitchak|feat(db): update CoursesWithSlides view to include slide IDs
8b72042a134c97083fb120f9d7837ca858d5b66e|2025-05-30 12:59:48 +0300|Pavlo Shepitchak|feat(database): add course slides migration and RPC functions
3ed30cc7283f98d333fa220aaf3adf56b80aa3ff|2025-05-30 12:50:13 +0300|Pavlo Shepitchak|refactor(courseRepository): update course save method to use RPC functions
29d2bf859a674cb9e5cf5e8dd79439313cea83be|2025-05-29 16:08:48 +0300|Pavlo Shepitchak|refactor(AiChat): implement TanStack Query for Gemini API calls
aa50f0ab55c720aa71080f7a56b1e0f3c0a9dc67|2025-05-29 15:00:59 +0300|Pavlo Shepitchak|refactor(AiChatScreen): reorganize code structure and improve type management
bc4f48812c7769ad63289a4dea427cd46a6a136b|2025-05-29 14:47:58 +0300|Pavlo Shepitchak|refactor(AiChatScreen): reorder functions in useAiChat hook
ef5111be052dcac8cd83dc1591ab0d57b2cd4e27|2025-05-29 14:46:26 +0300|Pavlo Shepitchak|feat(AiChat): refactor message creation into separate hooks
3ee67fa679eaab4e381095781c20652b83b64f58|2025-05-29 14:38:35 +0300|Pavlo Shepitchak|refactor(AiChatScreen): move flatListRef and auto-scroll logic to component level
550262e6ac340ca3a5a39650844301a0a2105aea|2025-05-29 14:35:34 +0300|Pavlo Shepitchak|refactor(AiChat): simplify chat initialization and improve error handling
bc4851257931a3751f1ba050965e5c4a50f3e6e0|2025-05-29 14:15:27 +0300|Pavlo Shepitchak|refactor: move createMessage function outside component scope
47dc80a0a89af0d8fabcdb246dcd23d6ebb227ed|2025-05-29 13:05:13 +0300|Pavlo Shepitchak|feat: integrate React Query for data fetching
5eadb59d4ffb6f82e968368d0e39d82cb1ea68b4|2025-05-28 12:57:50 +0300|Pavlo Shepitchak|fix(ui): replace hardcoded colors with theme variables
c7924f6ed88d163abbd6dd3c6d3c398755606094|2025-05-28 12:53:48 +0300|Pavlo Shepitchak|feat(HomeScreen): remove admin button from home screen
577f3a1d8678b1316d81765421d08d96af8ff9c1|2025-05-28 12:22:29 +0300|Pavlo Shepitchak|feat: temporarily disable CompanySelector component in HomeScreen
3defdc5ec1c0cae9d3a3949cec35b620192d7583|2025-05-28 12:09:37 +0300|Pavlo Shepitchak|refactor(Quiz): improve component structure and use Icon component
ec6b6939b9a3833b238e3135b786fb23bfc98b7d|2025-05-28 12:07:19 +0300|Pavlo Shepitchak|fix(quiz): improve answer feedback styling and localize result messages
daccfa44a6e55db1a974b9aeff882ba907614650|2025-05-28 11:57:46 +0300|Pavlo Shepitchak|refactor(chat): simplify chat input handling and API interactions
a0baa966195e59b597141fdecb46906e63ca2aca|2025-05-28 11:50:08 +0300|Pavlo Shepitchak|fix(AiChat): remove filtering of system prompts when sending to Gemini API
12a52e049594d1ac1af91080f652b9df41d3e0b5|2025-05-27 14:51:26 +0300|Pavlo Shepitchak|docs: remove Supabase configuration section from environment setup
1414e1b297a7bcf08f8400447a2360f7973f7d8d|2025-05-27 11:47:13 +0300|Pavlo Shepitchak|refactor(AdminScreen): extract components for better maintainability
a28f812723d3e35fc3814a75c8d5e2104e339a2e|2025-05-27 11:18:07 +0300|Pavlo Shepitchak|chore: update Supabase anon key with complete token value
70bd7a9666ddf74b7a47dd445d87a06a5c61074b|2025-05-27 11:17:58 +0300|Pavlo Shepitchak|fix(auth): remove navigation state check before routing
9727fdf33a25719fdeb686a17b00e701a6f66928|2025-05-26 16:56:04 +0300|Pavlo Shepitchak|ci: optimize GitHub workflows for better CI/CD performance
1eea0ecf1c1ee01173e92fc247d7f68d8b0f9843|2025-05-26 16:51:16 +0300|Pavlo Shepitchak|fix(config): update Supabase environment variable names
cd7d575a7776b9908fdbe48a9fe5fb63abda6167|2025-05-26 16:49:17 +0300|Pavlo Shepitchak|fix: update Supabase configuration variable names
933696f8ea7018708db7eee21a76e1c8a995ea97|2025-05-23 15:41:05 +0300|Pavlo Shepitchak|docs: update production plan backlog with completed security tasks
18d59aa247d2769d9c37b19aababd45356d8604d|2025-05-23 15:40:43 +0300|Pavlo Shepitchak|refactor: remove client-side Gemini API key references
6a194919855693341e9ebbe9ecbf325a853e625f|2025-05-23 15:31:46 +0300|Pavlo Shepitchak|fix: update Supabase environment variable references to use EXPO_PUBLIC prefix
eb7fc133e5a95604cef5d384e647795e2e97e6df|2025-05-23 14:59:49 +0300|Pavlo Shepitchak|docs: reorganize project documentation and update task status
5f80785f69c15642f44643cd7ad42bd2a99c89e0|2025-05-23 14:59:28 +0300|Pavlo Shepitchak|refactor: move Supabase credentials from env to app.config.js
4cfcee83113277f71fe75129fc667bbaefa81912|2025-05-23 14:37:59 +0300|Pavlo Shepitchak|chore: remove jscodeshift dependency
83d4b39b522096c97762921af2072441e2fe383e|2025-05-23 13:31:32 +0300|Pavlo Shepitchak|fix(ui): cast theme colors as unknown before ThemeColors type assertion
433a29cd20c0acbf0856274beca0619d609fa9a5|2025-05-23 13:29:46 +0300|Pavlo Shepitchak|fix(icon): ensure className is always a string in UIIcon component
3ffa0f9cee5f0be27ae4ad7577475f925907919d|2025-05-23 13:25:44 +0300|Pavlo Shepitchak|feat(spinner): improve spinner component with typed size constants
9b38d787ff3aa21bdaf3e6b16c030e07ca3ec6ce|2025-05-23 13:11:14 +0300|Pavlo Shepitchak|fix(VideoPlayer): update MuxPlayer reference type and playback rate setting
ffd0a2e401ae739e03c40f6d9ff2edd45b3e852a|2025-05-23 13:04:21 +0300|Pavlo Shepitchak|feat(CourseSelection): add optional onRefresh callback to CourseSelectionProps
42f733fb60d8850ed75dab7721daecf7832cf89c|2025-05-23 13:03:56 +0300|Pavlo Shepitchak|fix(LoginScreen): change GoogleSignInButton return type from JSX.Element to React.ReactElement
39fab709befc6270943c704f22dfbbfbc7885b2f|2025-05-23 13:00:27 +0300|Pavlo Shepitchak|fix(AiChatScreen): add proper typing to messages in AI chat functionality
15320dd02f4f33ce9e8ab2ad16304363e41eb5be|2025-05-23 12:51:37 +0300|Pavlo Shepitchak|feat(types): add image type declarations and format tailwind-variants config
8cefda05e85912f90878c7c9a2f910ad13234e76|2025-05-23 12:48:28 +0300|Pavlo Shepitchak|refactor: update slide initializers structure for video and completion slides
8e9ab976260b57255a4234cbc6a6c08f3ef7a86a|2025-05-23 12:33:22 +0300|Pavlo Shepitchak|feat: add tailwind-variants type definitions
f632a36cced947ab3a9fe2607fa31c1150206297|2025-05-22 16:18:37 +0300|Pavlo Shepitchak|I'll analyze the changes and create a commit message that summarizes the key modifications:
861704bd9bea1255c28fe4505c8da9a63fee5137|2025-05-22 13:58:32 +0300|Pavlo Shepitchak|chore(deps): update babel and playwright dependencies
dac2abccf6c5b5262b054801c3b87210b48fbf32|2025-05-22 13:57:53 +0300|Pavlo Shepitchak|chore(deps): update npm dependencies
6703962031bdcd0275c533739a00f05de645dcf1|2025-05-22 13:54:17 +0300|Pavlo Shepitchak|upgraded packaged
a93aff0588389782cefabda20cacf01853c83db0|2025-05-22 13:43:56 +0300|Pavlo Shepitchak|docs: rename production_plan_web.md to production_plan_backlog.md and update auth requirements
d7219dcefd92ec3e66d4753c6512541335728df1|2025-05-22 12:54:43 +0300|Pavlo Shepitchak|docs: mark Gemini API backend integration tasks as completed
d1d0681bc4ea60d77ad97f9e44c8a0360fb0209b|2025-05-22 12:29:31 +0300|Pavlo Shepitchak|chore(security): implement environment variables for API keys
53e96501848b37f36b4285a7a53d71ccf33bd211|2025-05-20 16:27:27 +0300|Pavlo Shepitchak|docs: add critical components for web implementation
e01fe8d01c281923bd89aeadec038c66c1c0217f|2025-05-20 15:33:41 +0300|Pavlo Shepitchak|docs: add production plan for KitFlow web version
ff5a258b82166cab1e628971ce43d71009fb6bb3|2025-05-20 15:23:24 +0300|Pavlo Shepitchak|docs: add production plan for KitFlow web version
349f329665d52d31b6012f88c84e3177fa61098f|2025-05-09 15:15:14 +0300|Pavlo Shepitchak|feat(CreatorTool): add refresh button for course list and update roadmap
184d6126857d6eda8333c6e0c437208b0ced24a4|2025-05-09 11:28:57 +0300|Pavlo Shepitchak|docs(roadmap): mark course storage feature as completed
f95911a21fbeb17b9addddac50bfa788f5d41e85|2025-05-08 16:05:03 +0300|Pavlo Shepitchak|style(CourseCard): add secondary color class to button text
c78a8b7e38cef99d41446c1fe48f1d3fa5195e3a|2025-05-08 16:00:05 +0300|Pavlo Shepitchak|docs: add Git file renaming guideline to windsurf rules
963e41599d930dd48d6e90723d7e0067c1afae97|2025-05-08 15:59:58 +0300|Pavlo Shepitchak|feat(CourseScreen): refactor and improve course screen functionality
58696977216ff00bdb80c787a9dd04df4fa83b9c|2025-05-08 15:54:00 +0300|Pavlo Shepitchak|feat(CourseScreen): implement dynamic course loading from API
32d04ae5bbda817d5580868e0891c55ba3c1c0a3|2025-05-08 14:01:10 +0300|Pavlo Shepitchak|refactor(CourseEditing): extract components for better maintainability
0584da5cddc010bd83f58751d844fc92248b4299|2025-05-08 13:58:59 +0300|Pavlo Shepitchak|feat(AiChatScreen): extract AudioRecordButton and SendButton components
ad733b9e30f70d94bfaf085578decdbb59b6f7ae|2025-05-08 13:57:29 +0300|Pavlo Shepitchak|docs: add refactoring documentation and tasks
3085bd96a44b2dfb5ba50315875861be36358fd6|2025-05-08 13:51:50 +0300|Pavlo Shepitchak|feat(profile): refactor ProfileScreen into modular components
3c466cb5b75939a48c44a9099ff7e4d6e60bdbfd|2025-05-08 13:48:05 +0300|Pavlo Shepitchak|refactor(SlideEditor): extract slide initialization logic to separate utility
e1fa29cdeac2a4ace7f30a481e60b19d4e37cca1|2025-05-08 13:46:15 +0300|Pavlo Shepitchak|refactor(AiChatScreen): extract MessageActionButtons component
511d57ee9da469213a57f3a630610bb272ab1847|2025-05-08 13:43:32 +0300|Pavlo Shepitchak|refactor(CoursesScreen): redesign CourseCard and improve StatusDisplay components
ff1e86395d589741993771fba41215bf7977c3f0|2025-05-08 13:22:30 +0300|Pavlo Shepitchak|refactor(CoursesScreen): extract components for better organization
2e28790b6033687d427149fd633ca31292959eec|2025-05-08 13:02:28 +0300|Pavlo Shepitchak|feat: integrate course metadata directly into Course model
8c13c1897667bb21aba802e9d3227a3926303f98|2025-05-08 12:57:03 +0300|Pavlo Shepitchak|feat(courses): replace static sample courses with dynamic API fetching
390d87e48c4312c4bd77ad2b01b7fa71ac16a806|2025-05-08 12:45:22 +0300|Pavlo Shepitchak|feat(database): integrate Supabase for course storage and management
ac68d557c364e11ee182c4013ff6ccea96b1fddb|2025-05-08 12:21:37 +0300|Pavlo Shepitchak|fix(docs): correct relative paths in development roadmap
f732b950add13d47f2871dd46fc64d5f08600477|2025-05-08 12:15:48 +0300|Pavlo Shepitchak|feat(course-editor): implement slide editing functionality
1c36ff6ea9c0bd2e352f4357c84d93c2e2b8bceb|2025-05-08 12:05:27 +0300|Pavlo Shepitchak|docs: add ESLint warning fix instruction to windsurf rules
1f7d0b3de969581321427aeeb8bb9a227cf83e20|2025-05-08 12:05:20 +0300|Pavlo Shepitchak|perf(courseCreatorTool): reduce sample course loading delay from 500ms to 50ms
be2c9dff9892e49b23a2a242dcc695df4c225802|2025-05-07 17:18:35 +0300|Pavlo Shepitchak|docs(roadmap): convert feature file references to clickable links
c193c5a00d489e68196e8c56605ca8b4edb63361|2025-05-07 17:16:28 +0300|Pavlo Shepitchak|feat(course-creator): implement course preview component
5c240add79bd65989df566e1f383f33062227906|2025-05-07 17:08:10 +0300|Pavlo Shepitchak|feat(courseCreatorTool): implement course selection component
27d242f3547760031973db066154e50a7bd2ed8b|2025-05-07 17:01:28 +0300|Pavlo Shepitchak|feat(admin): add Creator Tool screen and menu item
7dfd4e33f8c31e4eba033b456efb0adf84b8bcd2|2025-05-07 16:54:21 +0300|Pavlo Shepitchak|docs(courseCreator): add creator tool feature and update roadmap
c72775341adb768b40411049983d60c82679ef0e|2025-05-07 15:17:20 +0300|Pavlo Shepitchak|refactor: move course creator tool documentation to dedicated docs folder
51ed48c863bd3232d981988bc0fbde6b16bfa832|2025-05-07 14:05:40 +0300|Pavlo Shepitchak|refactor(courseCreatorTool): reorganize feature specifications into granular files
1a7844489b86fe4afbd0813ed977259c99dd56a1|2025-05-07 13:30:51 +0300|Pavlo Shepitchak|docs(courseCreatorTool): remove outdated documentation and add feature specifications
5ebda1cc4629a4b24bba16323b8f32dd4c4c338c|2025-05-06 13:20:21 +0300|Pavlo Shepitchak|docs: add import statements for all UI components
08699e03eeb4f1a879560a4a8bbbb098a695f258|2025-05-06 11:58:57 +0300|Pavlo Shepitchak|"Add initial documentation for AI-based course creator: development roadmap, technical specifications, and design concept."
546393cba3087ca5917da99f119fba6b7dc461fb|2025-05-06 11:47:51 +0300|Pavlo Shepitchak|docs(courseCreatorTool): simplify course creation functionality requirements
775a2653d389a1d63a95a9ffa479d7145b952e3f|2025-05-05 16:49:09 +0300|Pavlo Shepitchak|docs(courseCreatorTool): update functional specification for AI course creation
6fe35c4932ec6e871f9b9ab2c6b3cf1966b55ce4|2025-05-05 16:37:37 +0300|Pavlo Shepitchak|docs(courseCreatorTool): add initial concept documentation
6efc465629de017e390f7398d61ee420e2bba9d9|2025-05-05 13:28:41 +0300|Pavlo Shepitchak|fixed bug with recorder
d14bf3245f944ef9a0f539ced4a6faa1aa1009d5|2025-05-01 14:54:06 +0300|Pavlo Shepitchak|fix(VideoPlayer): add ts-ignore comment for viewRef in web component
8b57010d9e5f12d9f75a4f4c5f2e7d94d5eb1d2e|2025-05-01 14:52:46 +0300|Pavlo Shepitchak|fix(AdminScreen): improve loading state rendering in PromtTestScreen
d37732b4d2d679141bc89b50e91fb66fc2331ab3|2025-05-01 14:52:24 +0300|Pavlo Shepitchak|refactor: remove unused menu components and fix TypeScript errors
1797f230847191da3f98cf4c31dcc74ea3b8a657|2025-04-30 17:39:08 +0300|Pavlo Shepitchak|fix(courses): update video configuration format in developer sales course
d2809c0aecdbc82529d1688fa1b8d5abd5d37bff|2025-04-30 17:07:17 +0300|Pavlo Shepitchak|feat(course): add developer sales course
ffdc88693b1ca6b082ab09beacd2725dbe795657|2025-04-30 15:50:02 +0300|Pavlo Shepitchak|feat: add script to delete GitHub workflow artifacts
68c67de0383d7ab307fbe0b7fc99ad617cac401c|2025-04-30 13:45:05 +0300|Pavlo Shepitchak|feat(VideoPlayer): add mux and thumbnail props for web consistency
cf6e77037222f9c6e33ba0d28e99f861a00ed2ac|2025-04-30 13:44:03 +0300|Pavlo Shepitchak|fix(ui): update button icons and spinner components
ee65bc913d479b07e054c724dc2937ca29b7b28f|2025-04-30 13:34:08 +0300|Pavlo Shepitchak|I'll implement the requested UI improvements to make the app more modern and visually appealing.
9d7e9d205f4d7f2b6830bc0f6d5e054bf867ecfe|2025-04-30 13:21:40 +0300|Pavlo Shepitchak|chore: update app slug and EAS project ID
f648d03879492d4545f06102bf3fb7ba1f72d2d2|2025-04-30 13:00:38 +0300|Pavlo Shepitchak|ci: add dependabot configuration for npm and github-actions
8f25ce6f503be99175923b63d3f2efb67ddab90e|2025-04-30 10:47:46 +0300|Pavlo Shepitchak|style(LoginScreen): change background color from 'bg-background' to 'bg-white'
35e4cc302e99290383030710ed0e9b3404866764|2025-04-30 10:46:21 +0300|Pavlo Shepitchak|fix(AiChatScreen): simplify course context view and remove debug log
2a25812104416bfb8ba55526638385bdc5a99e60|2025-04-30 10:41:21 +0300|Pavlo Shepitchak|refactor: replace custom icons with Lucide React Native icons
2f72382399fcf9fcc745c078501cdf9c206f58e8|2025-04-30 10:38:11 +0300|Pavlo Shepitchak|feat(AdminScreen): replace Ionicons with Lucide React Native icons
dfeda21d67ca8896828ef75dc878909c162ff5db|2025-04-30 10:31:44 +0300|Pavlo Shepitchak|refactor: remove unused PaperclipIcon import in ActionButtons component
89eb2f6a42a1a72adaa2ff34e75e8c79c0c5915b|2025-04-30 10:31:30 +0300|Pavlo Shepitchak|docs: consolidate icon documentation into a single guide
42a8092322e7eb22802eb65764971eda99d5132d|2025-04-30 10:26:54 +0300|Pavlo Shepitchak|docs: update icon documentation with comprehensive guides
3b2ef6078872d26a38b45756f626de3f62bfd8d7|2025-04-30 10:15:52 +0300|Pavlo Shepitchak|feat: add lucide-react-native package for icon support
bdb4d6e1e9c500a1193b5635b6b1706c89cfb0d6|2025-04-30 09:54:29 +0300|Pavlo Shepitchak|docs: add Icon component documentation with usage guidelines
bb190e5b9a5d359c3df0bff869ea97ffa4864bfa|2025-04-29 16:33:04 +0300|Pavlo Shepitchak|refactor(AIInstructionsScreen): update action buttons with custom icons
f7a4474869bd1207049aabc2f42272ca93841e69|2025-04-29 16:21:08 +0300|Pavlo Shepitchak|refactor(AdminScreen): replace custom Icon component with direct Ionicons usage
e1c4a2c8e51f4ae2377f7fd86cc4a394e83977da|2025-04-29 15:53:14 +0300|Pavlo Shepitchak|fix: rename project from kitflow to kiflow
454fbf3e62091ddb02fcf29474c072f4cd1389cf|2025-04-29 15:50:35 +0300|Pavlo Shepitchak|fix: rename project from kitflow to kiflow
30f9606efa76d173154a1d718716b6c832ab7e37|2025-04-29 15:45:10 +0300|Pavlo Shepitchak|refactor: optimize code and fix potential memory leaks
3ba97a1b686cfcfb1b9003a9ff22dc1efb4fc8cb|2025-04-29 15:37:20 +0300|Pavlo Shepitchak|``` style: apply prettier-plugin-tailwindcss formatting
92fa1a9498d3bc3290465de3fabf077d8e789937|2025-04-29 13:27:44 +0300|Pavlo Shepitchak|refactor(ui): update button styling to use consistent variants
2f43ef28c4c91434b2568005c268985b42999bdb|2025-04-29 13:14:18 +0300|Pavlo Shepitchak|Update theme tokens to improve color consistency
77e32c27fd20c28bd589352988e92d319664b6f2|2025-04-29 13:08:22 +0300|Pavlo Shepitchak|Update ESLint command to use npx for consistency
f2ecd0ce573afb629055fbd25a779b1322a629d2|2025-04-29 12:31:25 +0300|Pavlo Shepitchak|feat(CourseScreen): add URL-based slide navigation and state persistence
6e8c719d17560be8271f12b6f11f8e0acecd5f54|2025-04-29 11:49:47 +0300|Pavlo Shepitchak|Refactor message creation to support additional parameters
efb28dc9a851181a27e1e9f7f50caa3196c79f06|2025-04-29 11:32:01 +0300|Pavlo Shepitchak|style(MessageBubble): improve chat bubble appearance with rounded corners and padding
d48a4bdebdf86fbadf63102b30844131f59c6789|2025-04-28 19:03:08 +0300|Pavlo Shepitchak|style(MessageBubble): improve chat bubble appearance with rounded corners and padding
cc9e362ff1d34608d0b56b171706fa860c59142e|2025-04-28 19:02:07 +0300|Pavlo Shepitchak|feat(AiChat): implement system prompt handling and message filtering
9563b83dccddb959fe1807b25705ee82524981ca|2025-04-28 18:47:26 +0300|Pavlo Shepitchak|docs: add eslint fix command to windsurfer rules
6ee64ca17fc6ec209a9f039d5489f65fc40457b6|2025-04-28 18:46:47 +0300|Pavlo Shepitchak|refactor(AiChatScreen): improve AI chat message handling logic
9f14cd745311e42c80f0124a2d7770d33c74b229|2025-04-28 18:32:30 +0300|Pavlo Shepitchak|refactor(AiChatScreen): improve code organization and error handling
04c6cc2fb02f46f3eb7fa0715e6b0ccd1a1b418f|2025-04-28 18:26:10 +0300|Pavlo Shepitchak|feat(chat): redesign chat input and audio recorder UI
e6610a8be85acea44f5e7b528f2c6671afed9a4c|2025-04-28 17:25:42 +0300|Pavlo Shepitchak|feat(AiChat): configure Gemini model parameters
3182fd58cb5615d33cd6174359e92f9170edefd0|2025-04-28 17:03:49 +0300|Pavlo Shepitchak|feat(AiChatScreen): improve system prompt structure for better mentoring experience
5cc4ce5842ffbdbe3889e7d1d3cf6fc03ce65918|2025-04-28 16:39:24 +0300|Pavlo Shepitchak|refactor: reorganize course slides and standardize UI spacing
24f6c645200977cf534b7405236955b908b354be|2025-04-25 18:01:41 +0300|Pavlo Shepitchak|style: standardize spacing with Tailwind tokens
964df6855c09aabc51d46d7a6d3b71307e37d9bd|2025-04-25 17:51:58 +0300|Pavlo Shepitchak|style: replace hardcoded colors with semantic color values
2c6522cbe281962f62d04c06b293d26fad6347ac|2025-04-25 17:25:34 +0300|Pavlo Shepitchak|refactor: remove custom color management in favor of Tailwind classes
ee9deb2cfa641485ecd49f272d4803493d8daebf|2025-04-25 17:21:44 +0300|Pavlo Shepitchak|refactor: remove CourseScreenTheme in favor of tailwind classes
072290f635914f806dd09d9007a849e5e03fd6b2|2025-04-25 17:17:36 +0300|Pavlo Shepitchak|refactor(Quiz): replace direct theme imports with Tailwind classes
b05cae78b3f984a6e9ca74493c5eacdb57961f70|2025-04-25 17:15:30 +0300|Pavlo Shepitchak|refactor(CourseScreen): replace theme constants with Tailwind classes
f906e027edda614a8fb069ae7e086627df90af34|2025-04-25 17:12:07 +0300|Pavlo Shepitchak|build: update TypeScript configuration and dependencies
3fac91afe2680d5375fab7bc420ae6973f5c70d7|2025-04-25 17:11:57 +0300|Pavlo Shepitchak|refactor: replace RealEstateSimulatorTheme with Tailwind classes
55e248114738ae5dbd5c07c70e24993ebf4fd2b8|2025-04-25 17:04:33 +0300|Pavlo Shepitchak|refactor: remove theme files and switch to Tailwind colors
0d61e08be99ad2593fa32eeb4ca0807806c4a559|2025-04-25 16:48:30 +0300|Pavlo Shepitchak|refactor(theme): remove unused typography and layout styles from RealEstateSimulatorTheme
911e4596016cdfb549d58e822c2602e9172c8bd9|2025-04-25 16:42:15 +0300|Pavlo Shepitchak|Remove unused typography and layout objects from CourseScreenTheme
104f4b30e1d531169a18f15ca4428175bfbd0d73|2025-04-25 16:41:26 +0300|Pavlo Shepitchak|refactor(AdminScreenTheme): simplify theme to only include menu item colors
bf61af22103f0780b8371011852a8c381e267dd2|2025-04-24 15:53:58 +0300|Pavlo Shepitchak|refactor: remove HomeScreenTheme and update icon styling
221b5719c8d1e88432a0bca7db64cb8296849bbd|2025-04-24 15:49:42 +0300|Pavlo Shepitchak|updated colors
58e431899ca42cc040e463d439f0c0f8fb761a1d|2025-04-24 14:09:39 +0300|Pavlo Shepitchak|feat(sharing): replace React Native Share with Expo Sharing
578f731dd211b3d6ecf8db2c88eab534588b0ea3|2025-04-24 13:50:52 +0300|Pavlo Shepitchak|fix(AiChatScreen): remove SwipePagination component and related props
e3ddd423b9a4d8549075386b0169ca3fd9207cee|2025-04-24 13:33:42 +0300|Pavlo Shepitchak|refactor: migrate components to use tailwind classes
73684162c2f09a1297cbc02ba39a8c7ba30700b1|2025-04-24 13:28:35 +0300|Pavlo Shepitchak|refactor: replace View with custom component and remove comment
7d8e666902c05736e056c06ad4102e4f9efcf39e|2025-04-24 13:25:01 +0300|Pavlo Shepitchak|refactor: migrate UI components to use gluestack v2
7eda6120ec29458d4c548cb70bfeeea005d0b409|2025-04-24 13:11:05 +0300|Pavlo Shepitchak|refactor(auth): replace custom GoogleSignInButton with UI component
99b42b506bd024ace9b4eeefbab872167944bd05|2025-04-24 13:10:37 +0300|Pavlo Shepitchak|refactor(auth): replace custom GoogleSignInButton with UI component
66c761fe8e5c042941b26195f5a4ea2bb3873908|2025-04-24 13:10:30 +0300|Pavlo Shepitchak|refactor(AiChatScreen): migrate to Gluestack v2 components and use className
47cd62ded19f980b47ccf2987c00515e969d111b|2025-04-24 13:01:26 +0300|Pavlo Shepitchak|refactor(SimulationScreen): replace StyleSheet with NativeWind classes
c8d7ebf7f08dc79cd11bbc8b420af5096c41d581|2025-04-24 12:51:31 +0300|Pavlo Shepitchak|docs: improve component reference guidance in windsurfrules
75790f37cb753b7bae10357d3245b9dd72ff76c3|2025-04-24 12:51:25 +0300|Pavlo Shepitchak|refactor(ForgotPasswordScreen): migrate to UI component library
cd20ba64c586391814b2a2a430356b4c1fdb59c9|2025-04-24 12:14:07 +0300|Pavlo Shepitchak|docs: update UI components documentation for gluestack v2
fadc6696146228c9c0532e244e0049acb74b3886|2025-04-24 12:00:45 +0300|Pavlo Shepitchak|docs: add rule for updating components documentation
846fdee60e2774987906fca05ad7b3a5deb4020b|2025-04-24 11:56:22 +0300|Pavlo Shepitchak|fix: update icon imports in ContentWithExample component
cc99f36c96b305ecb07b518d263e987ac00ae586|2025-04-24 11:52:53 +0300|Pavlo Shepitchak|refactor(CourseScreen): migrate ContentWithExample to use UI components
08375d1fc0af891504b35e044991b47d52834aba|2025-04-24 11:41:57 +0300|Pavlo Shepitchak|docs(ui): enhance component documentation with detailed props and examples
f2cab84f2f44e7f08605dc7bb5915bb706c810a2|2025-04-24 11:37:12 +0300|Pavlo Shepitchak|docs: update windsurfer rules with refactoring guidance
032510627b67a95578daa39f879f657bbe568ae5|2025-04-24 11:34:32 +0300|Pavlo Shepitchak|refactor: move UI components to src directory
947bf0e4bd1138343271f5a918bbb4d7dc78971f|2025-04-24 11:33:14 +0300|Pavlo Shepitchak|refactor(VideoPlayer): clean up code and convert inline styles to className
b765c21160080e7e40d12f584630d79d92e7ad5e|2025-04-24 11:23:52 +0300|Pavlo Shepitchak|refactor(VideoPlayer): simplify video player implementation
b6a3b21559ace758f6091eb0f717d63e578b5095|2025-04-24 11:18:15 +0300|Pavlo Shepitchak|docs: update WindsurfRules and add reference in guidelines
c96a38dd46586f69c98895478daada1187c0ea4b|2025-04-23 17:15:03 +0300|Pavlo Shepitchak|refactor(RealEstateSimulator): migrate ClientSelectionScreen to use UI components
8381b41e3b62ba8e9e9428aa78b0df9f5eb3a637|2025-04-23 16:35:02 +0300|Pavlo Shepitchak|refactor(AdminScreen): migrate PromptTestScreen to use Tailwind CSS styling
2b2fabb6d283ced8d5380b9a9960dedf872ee9c7|2025-04-23 14:13:50 +0300|Pavlo Shepitchak|ci: deploy to production environment with EAS
809722c7c4996fbe3e2037056cb60f9086e56248|2025-04-23 13:43:17 +0300|Pavlo Shepitchak|feat(ai-chat): replace Gemini SDK with newer version
e0a07342882fc02a24d8d26dd2bc5c02a07ab736|2025-04-23 13:16:21 +0300|Pavlo Shepitchak|refactor(AiChatScreen): improve chat session management
73b6fa179fe0f5bc72d47daa602f1d3186313e28|2025-04-23 12:50:28 +0300|Pavlo Shepitchak|feat(AiChat): implement Zustand store for chat messages
85ffbddc3b40bd4d3a2c99a2f3fa890155422912|2025-04-23 12:17:35 +0300|Pavlo Shepitchak|feat(ui): enhance audio recorder button with glass morphism design
0b48b173a9d754b662966269a9ba2e1447e4a116|2025-04-23 12:12:29 +0300|Pavlo Shepitchak|docs: add rule about removing unneeded files
93d284c4d19e9b141932c5044765f74cca5ff7fc|2025-04-23 12:10:43 +0300|Pavlo Shepitchak|feat(chat): add audio recording functionality to AI chat
f9610cc05356b5f26658b0717eb71f5dca2dbd8b|2025-04-23 11:50:19 +0300|Pavlo Shepitchak|refactor(AiChatScreen): remove unused prompt prop from AiChat components
d9c0590e93bc54c08a92790602469a720613ddc5|2025-04-22 16:56:58 +0300|Pavlo Shepitchak|feat(company): add company selection and conditional UI
b056e7aaa482b8ac8b1b6cedf623449fd9828da6|2025-04-22 16:48:50 +0300|Pavlo Shepitchak|docs: update code style rules in .windsurfrules
dcf59c8209c239ca1b8a6c2d4e5e82e86bfea223|2025-04-22 16:31:22 +0300|Pavlo Shepitchak|docs: add proximity-based file structure rule to windsurfer guidelines
6fabfff2c5f97459feb7300f4ef1d46198f01c84|2025-04-22 16:29:02 +0300|Pavlo Shepitchak|feat(VideoPlayer): implement useInView hook for automatic play/pause
f683d297a0a4e8a5fd90ff767a61a050d8d10b42|2025-04-22 14:34:15 +0300|Pavlo Shepitchak|feat(AICourseChat): refactor system prompt creation and add storage keys
2b2ee2d4b2bc0e580b3462572b7372670bbfd351|2025-04-22 14:20:29 +0300|Pavlo Shepitchak|feat(CourseScreen): add AICourseChat component for AI slide handling
0ad00eb489aa180469dcab3035add70629525170|2025-04-22 13:51:02 +0300|Pavlo Shepitchak|feat(CourseScreen): add AICourseChat component for AI slide handling
d8ee5d374b4b7c9b515560f977630cce9d8c35ab|2025-04-22 13:38:15 +0300|Pavlo Shepitchak|chore: update .windsurfrules with coding standards
5a35c500ae63ad42bf7def3858d8a47adc1a4b31|2025-04-22 13:17:03 +0300|Pavlo Shepitchak|feat(VideoPlayer): add playback rate control and autoplay
d665d3a83f15751fcf9036b872776aaa17cc2b1c|2025-04-22 13:09:28 +0300|Pavlo Shepitchak|refactor: move files to new folder
81ca50040c4cb3aa5a7a20f3070bc4855c251110|2025-04-22 12:58:48 +0300|Pavlo Shepitchak|feat: add Mux IDs for missing videos in furniture course
1fc4b13566957988d45decbaf7db8706653b5ce9|2025-04-18 16:38:22 +0300|Pavlo Shepitchak|feat: add module 3 lesson 3 video and update video reference in prompt
b3fc98a05a2dd53e5066647a9eac8529bf62c984|2025-04-18 16:23:25 +0300|Pavlo Shepitchak|feat(furniture-course): add Module 2 Lesson 3 content and video config
162817d8f4f3a4a61ce7bd79a3268a984fb12b49|2025-04-18 14:10:31 +0300|Pavlo Shepitchak|feat: centralize video configuration for furniture course
8abba6ede4b5aa067977135ad60fab67ebdd07a6|2025-04-18 13:25:52 +0300|Pavlo Shepitchak|fixed mux video
f6e8959d49832750fdff7cb23d017c94d339ebb6|2025-04-17 16:00:49 +0300|Pavlo Shepitchak|fixed prompt
6ecb8890f776704c1a3f85b31379643e68373f79|2025-04-17 15:49:52 +0300|Pavlo Shepitchak|feat(courses): add furniture sales course and text slide component
263c5c9e98a513f02dd9dacf9f4afb5dc5271596|2025-04-17 15:16:57 +0300|Pavlo Shepitchak|feat: add fallback for missing video content
ad63d25a6060a773b77c6e5b93a6afa9766e3956|2025-04-17 15:13:47 +0300|Pavlo Shepitchak|docs: clarify string literals styling rule
5872d5192ed445fb8220b72338f0d170369ef0e5|2025-04-17 15:08:25 +0300|Pavlo Shepitchak|refactor: change module.exports to ES module export in tailwind.config.js
f352b94df4cb83f2ae57ef93ded15756d0c38467|2025-04-17 15:00:39 +0300|Pavlo Shepitchak|refactor(AiChatScreen): simplify chat initialization process
85463196f5e9e2478af8c1f141708c9b1d3c84ab|2025-04-17 14:35:11 +0300|Pavlo Shepitchak|feat(prompts): add createCourse.md template for JSON course structure
691007a9b8390aad72a1def6320ad83ff267c0ab|2025-04-17 13:25:14 +0300|Pavlo Shepitchak|fix(prompt): rename 'question' parameter to 'prompt' in system prompt generation
04a7f4109edc8d166deea3aa6c1cc353bc712d27|2025-04-17 13:18:43 +0300|Pavlo Shepitchak|refactor(AiChat): simplify system prompt handling logic
f6d0b2e9a6d3f5b6d5599911886c24737b11af78|2025-04-17 13:07:38 +0300|Pavlo Shepitchak|refactor: remove unused onAnswer callback from AI chat components
8b4f3e176854d05c5bb090c4c53bb712b7b5f149|2025-04-17 12:17:22 +0300|Pavlo Shepitchak|refactor(AiChatScreen): rename 'question' prop to 'prompt' for clarity
50c37a74dd6da7478f0c6cccdd44bda2125cc764|2025-04-17 12:06:05 +0300|Pavlo Shepitchak|refactor(AdminScreen): extract menu items to separate file
75a9ff7da03be8ba5f090ac741005411edc045c7|2025-04-17 12:05:55 +0300|Pavlo Shepitchak|refactor: remove style properties from deregaCourse slides
a9c1f38b82b9bb9c04fe87d81e2922dfb0112489|2025-04-16 17:07:27 +0300|Pavlo Shepitchak|feat(AdminScreen): implement pixel art design style
a17af82803073d05a0482b4f70027018fa6f00ac|2025-04-16 17:02:16 +0300|Pavlo Shepitchak|styleguide
57a6523483ad5d49f69971d9399f498c7b161805|2025-04-16 17:00:38 +0300|Pavlo Shepitchak|docs: add UI components documentation and update windsurf rules
4c220585fbae9ce4dccf89310c2ee538e8d567c9|2025-04-16 16:53:46 +0300|Pavlo Shepitchak|refactor(AdminScreen): migrate to UI component library
f9613b19529fecccc65039dc31130f2b7b49e73c|2025-04-16 16:53:38 +0300|Pavlo Shepitchak|chore: add .windsurfrules configuration file
bb8e40c12c55ffa45354f457c520b56695b55420|2025-04-16 11:19:01 +0300|Pavlo Shepitchak|refactor(RealEstateSimulator): migrate PropertySelectionScreen to use UI component library
62f122398f9cd502efdfac0205894fe72a0c71db|2025-04-16 11:12:32 +0300|Pavlo Shepitchak|remove(screens): delete RealEstateSimulator MainScreen component
d5a0853ceebe27e77d8beefb90dfed4b9a9d6d7d|2025-04-16 11:02:28 +0300|Pavlo Shepitchak|refactor(ProfileScreen): migrate to UI components and apply pixel art design
f182f6cba9b229f51988b925d0601f20b35187ad|2025-04-16 10:52:58 +0300|Pavlo Shepitchak|refactor(CoursesScreen): migrate to tailwind styling and UI components
0b0276dc2e985b7ef78000809c607e859fc499bc|2025-04-14 16:59:49 +0300|Pavlo Shepitchak|refactor(AiChatScreen): extract components for better maintainability
c0414cc0eac9bcffc18f98dc731703d60a76af64|2025-04-14 16:52:36 +0300|Pavlo Shepitchak|feat(AIInstructionsScreen): refactor into reusable components
d98e12d18d0f6b3e5d364262eea3db8bf8edea6b|2025-04-14 16:22:54 +0300|Pavlo Shepitchak|refactor(AIInstructionsScreen): replace external styles with inline tailwind classes
e7c57bdc9dcb8ce9ee766dbfb4fbbbdc03d811ba|2025-04-14 16:18:45 +0300|Pavlo Shepitchak|refactor(AIInstructionsScreen): extract styles to separate file and add renderInstructionField component
958f63ffd041a8c0cd5b5c3b19bdd4c99687a8ff|2025-04-14 13:12:00 +0300|Pavlo Shepitchak|feat(AIInstructionsScreen): add reset fields functionality
cf593648dd5f7972e06e53f08b0961d0f441e0b7|2025-04-14 13:09:52 +0300|Pavlo Shepitchak|feat(AIInstructionsScreen): add confirmation dialog for template loading
cd91ab6d91d235ceb70a3bf5223697e917eb959d|2025-04-14 13:06:58 +0300|Pavlo Shepitchak|feat(AIInstructions): add template loading functionality
1685eba29b62f6196d24ccd335d5e00ec55614c5|2025-04-14 12:51:47 +0300|Pavlo Shepitchak|feat: add prompt test screen route for admin section
2c7744450c2fc406ec3aa1c22ad88fb715346214|2025-04-14 12:51:31 +0300|Pavlo Shepitchak|refactor: remove unused company profile functionality
447f6fcf0f1ed887e6bc3a54e766ceec0491ec74|2025-04-14 12:47:25 +0300|Pavlo Shepitchak|feat(admin): add PromptTestScreen for system prompt testing
88efc35d0e4e36d372066da5f445ab0507ed626e|2025-04-14 12:30:26 +0300|Pavlo Shepitchak|refactor: remove duplicate AiChatScreen component and enhance promptService
84716f8fd934c94f607cab5f63e45866b48232aa|2025-04-14 12:19:04 +0300|Pavlo Shepitchak|feat(AiChat): extract prompt handling to dedicated service
4524e5e60fce57db94e7c3389ed7e90d59f13808|2025-04-14 12:14:43 +0300|Pavlo Shepitchak|refactor(CourseScreen): replace Question component with AiChatScreen
4b7e97fe646f6ae7b64705761c8767dc7767934b|2025-04-14 12:06:53 +0300|Pavlo Shepitchak|feat(chat): implement AI chat screen component
50023512140e5b45e9c7214b4f955c7d33152e6f|2025-04-14 12:01:08 +0300|Pavlo Shepitchak|refactor(AIInstructionsScreen): move component to separate file
eff25bafef1171a1b9426aeeaded7ae9f7af20a7|2025-04-14 11:55:33 +0300|Pavlo Shepitchak|feat(AIInstructions): restructure AI instructions into categorized sections
3d92d8d0a4e3a79464b7eb67463a58fe9a6cd59b|2025-04-14 11:47:36 +0300|Pavlo Shepitchak|refactor: rename company-profile to ai-instructions
cab8c37dcbf8fb9e5762494840141b61c9c271dc|2025-04-14 11:41:57 +0300|Pavlo Shepitchak|refactor(CompanyProfileScreen): replace inline styles with Tailwind classes
6b54cd3449558821b405ef34a547fd7edc23f871|2025-04-14 11:32:13 +0300|Pavlo Shepitchak|refactor(CompanyProfileScreen): migrate to UI component library
724b2b2aefa9ac9bcebfa555fbdfe69da425f112|2025-04-11 17:15:45 +0300|Pavlo Shepitchak|deploy
ab1c6354f29aa6455664d75984413d11ebb713cd|2025-04-11 17:14:43 +0300|Pavlo Shepitchak|new homescreen implementation
8f5de4270dc33c83e7e240e81bebf861b8590361|2025-04-11 16:36:06 +0300|Pavlo Shepitchak|gluestack
77376f1baa6a90015e9f9272df49fbbb08e68507|2025-04-11 13:51:29 +0300|Pavlo Shepitchak|deploy
d727676d336564fb840a6c38d13511515d64d1fe|2025-04-11 13:48:55 +0300|Pavlo Shepitchak|push
a5460e65647b6ddcf9ece53e800306a687da4d46|2025-04-11 13:46:04 +0300|Pavlo Shepitchak|eslint
be4d68d507aa068b66f6f295a4b333f2562341a9|2025-04-11 13:44:45 +0300|Pavlo Shepitchak|fixed view
a2f43d4ceb1037c25954941909b7877577002c6a|2025-04-11 13:27:00 +0300|Pavlo Shepitchak|simulation
d63d2b346385e0db972e2cb6f76c83a9f62662de|2025-04-11 12:10:42 +0300|Pavlo Shepitchak|fixed styles
d12f0247f4e0ac5045162afaf91c69ec3ab57992|2025-04-10 16:16:10 +0300|Pavlo Shepitchak|ts
34c63a4129ffe46690fcd3c6f3a9cee5a348097a|2025-04-10 16:04:43 +0300|Pavlo Shepitchak|tserrors
09d87954393687e4bbd95a29d6d8db754c6ceabc|2025-04-10 16:00:38 +0300|Pavlo Shepitchak|styles for company profile
8d481d301179aa7c4d6136c5ddf112a48d87db65|2025-04-10 15:49:34 +0300|Pavlo Shepitchak|home screen styles
5fac6f5ec5a4f7f0442fee0c80a8ae4548d2de2e|2025-04-10 15:45:23 +0300|Pavlo Shepitchak|fixed styles
14a6b3760764c4eaabf35c62ea948ed2a23d88f3|2025-04-10 15:42:30 +0300|Pavlo Shepitchak|fixed logo
92a9b670f858bd174d1e60dbcc284e122bd4c52b|2025-04-10 15:40:13 +0300|Pavlo Shepitchak|fixed margins
3364849c33264e1d771d0c521924d546adaa0af3|2025-04-10 15:29:07 +0300|Pavlo Shepitchak|new home screen
17eb0c917b41ba4df1c1d2cdc08768155fc8ff0d|2025-04-10 15:25:42 +0300|Pavlo Shepitchak|eslint
2d36de847c06c45ffa3d7c53121f655b7a4d3d47|2025-04-10 15:23:16 +0300|Pavlo Shepitchak|admin screen
6f35079acfeec10bc73ad890ed93ba89788c51c8|2025-04-10 13:40:55 +0300|Pavlo Shepitchak|added balck theme
c6e6bb40619e02e9d25864011c801d8819f422fc|2025-04-10 13:34:59 +0300|Pavlo Shepitchak|theme support
52563c4c0c828fa068493262d0313b0fe7d2e624|2025-04-10 13:19:50 +0300|Pavlo Shepitchak|docs(styleguide): add mobile app style guide based on KFLOW image
971fabc97f89a1b365315624748f12554e915cee|2025-04-10 13:09:33 +0300|Pavlo Shepitchak|admin panel
5ae25cf4dc474731fde12d9e63676f9732e06bfd|2025-04-10 12:41:27 +0300|Pavlo Shepitchak|fixed web video
db04a0455860fe1d4549b7c5c342693bcb55f000|2025-04-10 12:21:00 +0300|Pavlo Shepitchak|feat(VideoPlayer): disable native controls and fullscreen mode
8f855d14d28ce8117238faf3143ee8ffe6efdc72|2025-04-10 12:03:17 +0300|Pavlo Shepitchak|chore: disable automatic authentication check on app load
43458b32a3bc94e37ee7938af570cc9f209d9161|2025-04-10 11:46:19 +0300|Pavlo Shepitchak|feat(video): replace custom video player with expo-video library
d7701b92d416c0f250d8dec8602e8aef8e9989bb|2025-04-10 11:45:56 +0300|Pavlo Shepitchak|refactor(video-player): rename VideoPlayer to VideoPlayerOld
500eec6cc2c521500b42f57e9789e48fae93ee94|2025-04-09 18:07:50 +0300|Pavlo Shepitchak|added videos
ad6adc5c4fb2a7683a735987fee312fce5abd745|2025-04-09 17:53:28 +0300|Pavlo Shepitchak|added videos
6be176e5fca39ad3d042434bd7b7a43060a1ee7e|2025-04-09 17:40:54 +0300|Pavlo Shepitchak|feat(courses): add hypothetical situation questions to sales department course
cebdc658ecf9e5984cf95e05f833c73c640f26dd|2025-04-09 17:32:50 +0300|Pavlo Shepitchak|fixed Ai
24efee46c59c282811c0e28086d0c490bd14f307|2025-04-09 16:17:26 +0300|Pavlo Shepitchak|fix(tests): add ts-ignore annotations to slide role assertions
367202de13ad0a75d1f6d5e73d64dfd227f54a2a|2025-04-09 16:15:20 +0300|Pavlo Shepitchak|fix(tests): update accessibility roles and expected colors in course screen tests
1d1008b88518a08a26ecc856de073585d794abdb|2025-04-09 16:03:07 +0300|Pavlo Shepitchak|fixed eslint
fd1046973031711074364f65156abcf3f6f1c541|2025-04-09 15:41:18 +0300|Pavlo Shepitchak|feat(ui): implement responsive layout for CoursesScreen
6728c3ef6393b62e90a3b1da55c6cb09cedaa9c7|2025-04-09 15:37:46 +0300|Pavlo Shepitchak|integrated ai scree
94c769a398dcd8c7a4e7e5f961fc88b431a4fec4|2025-04-09 15:18:09 +0300|Pavlo Shepitchak|feat(ui): implement responsive design for course screens
f71e0e6f70399aa9b5b07b3bddbeb8bf0f0e2a37|2025-04-09 15:04:44 +0300|Pavlo Shepitchak|fix: update import paths to use /src prefix
8162f74dbf814d2ed03576be7024fa1808527d0c|2025-04-09 15:01:29 +0300|Pavlo Shepitchak|feat(video-player): improve video player stability and add sales course
5a6c6f05986507b83496eed6fa64385def6f5f16|2025-04-09 13:44:48 +0300|Pavlo Shepitchak|refactor: migrate files to src and fix import paths
00bd1dcfb6e9c1fe56bbc52f0d2ca91e4139c871|2025-04-08 17:33:53 +0300|Pavlo Shepitchak|fixed test
7a48c737afc6d4eea216947f4c4b04b3bef92c3b|2025-04-08 15:12:42 +0300|Pavlo Shepitchak|playwrite CI
9f428370de134b14e97f05d005683ed9af115021|2025-04-08 14:58:58 +0300|Pavlo Shepitchak|playwrite CI
116f5aa30af9b37b33d1c169ddf7a1e24b83de50|2025-04-07 16:54:39 +0300|Pavlo Shepitchak|static errors
f3c8c6610a6d0727bb6d8f6e9d391cc61cb111b7|2025-04-07 16:44:05 +0300|Pavlo Shepitchak|feat(chat): implement real estate sales trainer AI simulation
edd0a00f34e71f205d16715f781d64a0cd1279d4|2025-04-07 13:11:44 +0300|Pavlo Shepitchak|feat(AiChat): refactor Gemini service into separate module
682bb896a1fadff611ca2c72e57774532f55f36c|2025-04-07 13:06:06 +0300|Pavlo Shepitchak|folder structure
0e81c6b6411cffbcad99fb4c9d2439afb9c1c94d|2025-04-07 12:29:22 +0300|Pavlo Shepitchak|feat(ai-chat): add AI chat feature using Google's Gemini API
aaeb13e6ce062cd0cbf972df9ba89452750a4877|2025-03-31 19:25:51 +0300|Pavlo Shepitchak|cache npm
90471ddb8dbef82bd195b873301a3a7c739e7896|2025-03-31 19:22:37 +0300|Pavlo Shepitchak|cache npm
4268a7be2eef1703d2a37c3fbab1c40c848d5875|2025-03-31 19:19:27 +0300|Pavlo Shepitchak|cache npm
1745bfa90b85361a743b3fe8518f63afb5ef610d|2025-03-31 19:16:27 +0300|Pavlo Shepitchak|fixed ci
305e09dff1c0ff2eccfcbd066f25d7c560b39833|2025-03-31 19:08:59 +0300|Pavlo Shepitchak|fixed ci
9adfe66a72672cb6ef218110e17889a7f6b77b06|2025-03-31 19:06:26 +0300|Pavlo Shepitchak|fixed ci
47952507d6551c2cb5f272ffb3d515b40842ae44|2025-03-31 19:01:06 +0300|Pavlo Shepitchak|fixed ci
1a43e8a9034da91f7d318dc58b004153485264ed|2025-03-31 18:47:38 +0300|Pavlo Shepitchak|fixed ci
cf3820f098aad82091fdad15bf356d503163bc35|2025-03-31 18:39:50 +0300|Pavlo Shepitchak|prettier
d67d297da8467374a9d30eac55be438c7bb36a17|2025-03-31 18:39:01 +0300|Pavlo Shepitchak|added playwrite
ec5f08e729b4ac68ea469820d0ae05ae92e5705c|2025-03-31 17:28:44 +0300|Pavlo Shepitchak|fixed warnings
8d16bf3feaf4d04cdfe0af86512bfbeee3875602|2025-03-27 15:08:47 +0200|Pavlo Shepitchak|eliminate master word
90e54b7a8c07308254f2223b23fc4f5e14ca0ddb|2025-03-27 14:58:15 +0200|Pavlo Shepitchak|updated ci file
894fe07c8517d8a40ff567b6e40d45136763d923|2025-03-26 12:55:56 +0200|Pavlo Shepitchak|docs
df53a3f021071aa979abfac6bae1250761b38e20|2025-03-26 12:36:31 +0200|Pavlo Shepitchak|chore: remove project reset script
af1149ae81a9a9b4d2c2f9423100e68dd6d331f7|2025-03-05 16:38:43 +0200|Pavlo Shepitchak|fixed package error
b8d310020f753e0df73ed0987a5d7ebf6e4053d1|2025-03-05 16:17:49 +0200|Pavlo Shepitchak|node
2b09d201461373c31c1d71eb654133375e0a7874|2025-03-05 16:15:42 +0200|Pavlo Shepitchak|deploy with owner
27eec7bebfcf2adc52e38bc09833946b5e90148c|2025-03-05 15:32:13 +0200|Pavlo Shepitchak|deploy
69a06bf4ffd1b77927fc4ca7f2334fa31f9a1777|2025-03-05 15:26:42 +0200|Pavlo Shepitchak|Merge remote-tracking branch 'origin/main'
4e8c48ee951d66ce4804e1526b0e5e386c4e3d78|2025-03-05 15:26:16 +0200|Pavlo Shepitchak|deploy
2b4b9fee33156d02364a90749a266f2b19a460f7|2025-03-05 15:24:02 +0200|Pavlo Shepitchak|deploy
77476e51867fd242a4b81a41a3bd1ce19a64efcd|2025-03-05 14:45:38 +0200|Pavlo Shepitchak|deploy
a9ed9f52e533a1e1acae6cba07c82ab0ef4f5ebd|2025-03-05 14:27:43 +0200|Pavlo Shepitchak|empty
f9f26042ac01ee97dcb0550d350748003f63f2ac|2025-03-03 15:06:20 +0200|Pavlo Shepitchak|fixed auth
d71c94f7d459524cdfe7a1d576e44f721ed860e0|2025-03-03 12:54:37 +0200|Pavlo Shepitchak|ci
26faf1764c5aa9e2402660a2f846fc857a714f5b|2025-03-03 12:43:34 +0200|Pavlo Shepitchak|ts actions
c438b3854d235f7f1478ad49f8d00866b6592cb0|2025-03-03 12:41:49 +0200|Pavlo Shepitchak|ts
159e09da00001be02a6d0ed8ae2e549cb3ef841a|2025-03-03 12:25:45 +0200|Pavlo Shepitchak|github actionsfix
1a50bdfa7b6cf7427cb2a041f17078a10bee4955|2025-03-03 12:22:52 +0200|Pavlo Shepitchak|github actions
2eaa05088194f6cc01af8511ce649dc7f56de225|2025-03-03 12:19:40 +0200|Pavlo Shepitchak|prettier
f3ebf8f85da4fe3b4dbab511b171460c2d152a6f|2025-03-03 12:07:59 +0200|Pavlo Shepitchak|eslint
56e438aae1cfe57f08fc6e64a772fc7337954006|2025-02-28 16:13:52 +0200|Pavlo Shepitchak|додано екран з курсами
ecd8dfc711918284f07447c4e2617774e8179e07|2025-02-28 16:04:57 +0200|Pavlo Shepitchak|added homescreen
d85854ab1ff399c13bc6c8a2993a49ad759aa9b5|2025-02-28 14:22:16 +0200|Pavlo Shepitchak|Remove unused components
4f5c2dd6eb6a38262ab44bd186abee5d6e1cee1f|2025-02-28 14:20:30 +0200|Pavlo Shepitchak|refactoring folders
c8abe3b53180b618fe1e935283de15147a3a2246|2025-02-28 13:54:00 +0200|Pavlo Shepitchak|Fix import paths and type errors after moving screens
7171fa9cf168904f9208676cf86d6c1a146ecf9a|2025-02-28 13:52:08 +0200|Pavlo Shepitchak|Add re-export files in original locations
b7db8a2683d094a364d0debaed5d689995c18c34|2025-02-28 13:50:22 +0200|Pavlo Shepitchak|Move and rename screen components to components/screens
c98e0ca335138446edb0761ff00fe93cda485b5b|2025-02-28 12:38:44 +0200|Pavlo Shepitchak|improved UI
6676aeaedf78cd0c515b99db5c9b28e2977152bb|2025-02-28 12:29:49 +0200|Pavlo Shepitchak|improved profile view
52e710eb65ac2431205e59becfdf27955c0e6e2d|2025-02-26 17:16:25 +0200|Pavlo Shepitchak|refactoring
7441566aa25de5b40ee4ab5a7450f42156e604fc|2025-02-26 17:06:18 +0200|Pavlo Shepitchak|fixed auth
3bc921a8b74fac5fe9b798bea36fa07df075d5ec|2025-02-26 14:02:02 +0200|Pavlo Shepitchak|google auth functionality
07678406686515117133c13d21bd0511923157e3|2025-02-26 13:56:43 +0200|Pavlo Shepitchak|google sign in button
ba8c391f05db855998ebcb8417c85472d00bbbb0|2025-02-26 13:39:22 +0200|Pavlo Shepitchak|login ui
235397d313a70ee18628ce1ba8e0b8cf445242c9|2025-02-26 13:18:57 +0200|Pavlo Shepitchak|logout
714a627e9282918b949a667d2391cb7e6b10b6d9|2025-02-25 16:23:38 +0200|Pavlo Shepitchak|profile screen
f4e63e861f83eed410285f1a2bf6074fa2fbb283|2025-02-25 15:56:01 +0200|Pavlo Shepitchak|added login screen
07c3d0429576ea32452b2776715a7383581cd476|2025-02-25 15:55:49 +0200|Pavlo Shepitchak|chore: update Expo and React Native dependencies
43e9a10f82ec5289e84d5b97dfbec26ff5d31539|2025-02-25 15:06:10 +0200|Pavlo Shepitchak|launch web
d756dee2bc3d1a225d9cde9f7b5ae00bb1d42a3c|2025-02-25 14:58:00 +0200|Pavlo Shepitchak|debugger
c179691f01998857d88e172bbab9bfd1ebc0d3c7|2025-02-24 15:53:52 +0200|Pavlo Shepitchak|launch
48f95ae4c074ab83307def52bdb2f385316db1be|2024-12-12 19:49:14 +0200|Pavlo Shepitchak|fixed success screen
029ed8647a35020b0108fdf98a7367eb91b95039|2024-12-12 19:44:42 +0200|Pavlo Shepitchak|fixed pagination
c3288ce13d95f49f19e57290970378eef4d78a6a|2024-12-12 19:40:46 +0200|Pavlo Shepitchak|video is fixed
aafa7523fbd1111205e69a325ac504c6c9db3439|2024-12-12 19:29:17 +0200|Pavlo Shepitchak|fixed form
21a5e8ccdec787093f3d0fe3324a51bf192ee826|2024-12-12 19:18:11 +0200|Pavlo Shepitchak|fixed summary scree
a8aa417128900fec4899be540f820d8faa402cd4|2024-12-12 18:41:04 +0200|Pavlo Shepitchak|fixed videos
425c47f03896a39213cb086a3bd46f45345f1208|2024-12-12 16:38:57 +0200|Pavlo Shepitchak|slides
d4fe8204cff5c9bf215806da42387cf4ee38ba58|2024-12-12 16:24:02 +0200|Pavlo Shepitchak|pagination centered
35f1af70ee65389f26e8774fad5145c101558a83|2024-12-12 16:19:49 +0200|Pavlo Shepitchak|added sections
7c295d87c0cda494258b4d002242b7e398d4ef50|2024-12-12 14:48:49 +0200|Pavlo Shepitchak|merge question and example
f86dfe5c35be1a52b72071c228c70612612a22a9|2024-12-12 14:02:44 +0200|Pavlo Shepitchak|4 slide
3fcbd5f7bff11d6d7d5a3021c2a408828dde6e53|2024-12-12 13:52:03 +0200|Pavlo Shepitchak|added input button
b4c9d08e23eed10bcb516c846f029a45ea7a5b9a|2024-12-12 13:19:18 +0200|Pavlo Shepitchak|refactoring
e2cea3966f77a0f0839c70b34b5e93c250b539b8|2024-12-12 13:06:14 +0200|Pavlo Shepitchak|started new course
7f1b387c24154aba6b8f33d31c57475464654a84|2024-12-12 13:00:26 +0200|Pavlo Shepitchak|refactoring
4d9e3f06bfa7e1e3dc607745cf960268fc94751a|2024-12-12 12:56:27 +0200|Pavlo Shepitchak|refactoring
54eb496f620107f79855811dd5811c47bc683fa0|2024-12-12 12:52:13 +0200|Pavlo Shepitchak|refactoring
0f3634a35203f4813ed356daf8cb1282f098b42d|2024-12-12 12:45:08 +0200|Pavlo Shepitchak|config
cbb0c1df6ff641c7fbc178355ca897f64b9ea9e4|2024-12-12 12:40:38 +0200|Pavlo Shepitchak|refactoring
bdc757ecaa9e8f2fb268ee0ad2da1d75a27059ce|2024-12-12 12:35:27 +0200|Pavlo Shepitchak|refactoring
9566dbd998980344fffaf906e23f2cffb463e4f8|2024-12-11 16:55:17 +0200|Pavlo Shepitchak|video added
6259a17d015e69209870c62081854bcd26ad3865|2024-12-11 15:16:44 +0200|Pavlo Shepitchak|refactoring files location
3c1afb75c244cd525444fa53935915dbb49b81db|2024-12-10 13:04:12 +0200|Pavlo Shepitchak|slides new
1d78ed8a976f72a1fb4beafe50cdf6a9f11210f0|2024-12-10 12:46:59 +0200|Pavlo Shepitchak|sharing
e16132c9525c73327bb80429461379fbcbff39c9|2024-12-10 12:34:46 +0200|Pavlo Shepitchak|text slide to separate screen
6fd72369085037a9d0bbb5b6a3f14b1152f3b49f|2024-12-10 12:05:27 +0200|Pavlo Shepitchak|completition screen is moved
46ef5e8f6ff07c5d407cbe69bdf9953ff358fac3|2024-12-10 12:02:40 +0200|Pavlo Shepitchak|moved quize
62425972f0b12ca0c2810e106ff736cdf1d78285|2024-12-10 11:57:59 +0200|Pavlo Shepitchak|feformat
324469ddbd9df17caa39c61c2b73a9d879043027|2024-12-10 11:56:09 +0200|Pavlo Shepitchak|play on tap
6a66d22ef985b0c1c74cd27897ffe5505d18e35e|2024-12-10 11:50:57 +0200|Pavlo Shepitchak|works pause
d1745dfd27438d01f422f78a1643341940304e1f|2024-12-10 11:37:37 +0200|Pavlo Shepitchak|refactoring
f6f979a703af5537d762b77b7ad896978f8b2f9d|2024-12-10 11:28:26 +0200|Pavlo Shepitchak|fixed video controlls
42e6b3d073fffbf843ae862689e3b09701dec53c|2024-12-09 16:01:26 +0200|Pavlo Shepitchak|video
3806190937193d4f1899d9a6018ccfafb29c18a7|2024-12-09 15:49:28 +0200|Pavlo Shepitchak|quize
0251a2892bdbdf74fdd3881bd6530e60c1f81c7b|2024-12-09 15:25:53 +0200|Pavlo Shepitchak|vertical indicator fix
8e846ea95261d737ee7248603173b0b5d9f3a3e0|2024-12-09 15:20:46 +0200|Pavlo Shepitchak|development build
381abae9a0c8ec7f800b81e3f7157dfecc57042e|2024-12-09 15:20:30 +0200|Pavlo Shepitchak|added video
3fbb2cb970bfab4d98bbe5925a855d9fc35f7310|2024-12-09 14:59:29 +0200|Pavlo Shepitchak|pagination to the different file
6d0de802ccd4a51bf00f46c04a853add90712983|2024-12-09 14:43:30 +0200|Pavlo Shepitchak|split files
493935defa97c26e2e554b608b81fe0f577db494|2024-12-06 17:03:23 +0200|Pavlo Shepitchak|good
b3effda2d649630d9cef31bd797abd2bf8077f70|2024-12-06 16:49:24 +0200|Pavlo Shepitchak|slides
a7894a17fe19e7b7a938fd00dca26a8dcefd1d82|2024-12-06 16:33:53 +0200|Pavlo Shepitchak|header hidden
e691fa33f1f751403dcf33dac94e1273ba008698|2024-12-06 16:30:39 +0200|Pavlo Shepitchak|prettier
1ab24bff06ed7e760d0106ceb6f902bb219b52df|2024-12-06 16:28:14 +0200|Pavlo Shepitchak|added scrolling
f255c688eccad9515e81e981468c0bd9715d8f2c|2024-12-06 15:09:05 +0200|Pavlo Shepitchak|Initial commit
name: Continuous Integration

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main, dev]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    name: ESLint Check
    runs-on: ubuntu-latest
    steps:
      - name: Setup workspace
        uses: ./.github/actions/setup-workspace

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}
          sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Run ESLint
        run: npm run lint

  type-check:
    name: TypeScript Check
    runs-on: ubuntu-latest
    steps:
      - name: Setup workspace
        uses: ./.github/actions/setup-workspace

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}
          sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Run TypeScript check
        run: npm run type-check

  build:
    name: Build Verification
    runs-on: ubuntu-latest
    steps:
      - name: Setup workspace
        uses: ./.github/actions/setup-workspace
        with:
          setup-expo: true
          expo-token: ${{ secrets.EXPO_TOKEN }}

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          expo-token: ${{ secrets.EXPO_TOKEN }}
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}
          sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Build for web
        run: npx expo export --platform web --output-dir dist-check
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Verify build artifacts
        run: |
          if [ ! -f "dist-check/index.html" ]; then
            echo "❌ index.html not found in build output"
            exit 1
          fi
          echo "✅ Build verification completed successfully"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: web-build
          path: dist-check/
          retention-days: 1

  status:
    name: CI Status
    runs-on: ubuntu-latest
    needs: [lint, type-check, build]
    if: always()
    steps:
      - name: Check results
        run: |
          echo "=== CI RESULTS ==="
          echo "Lint: ${{ needs.lint.result }}"
          echo "Type Check: ${{ needs.type-check.result }}"
          echo "Build: ${{ needs.build.result }}"

          if [[ "${{ needs.lint.result }}" != "success" ||
                "${{ needs.type-check.result }}" != "success" ||
                "${{ needs.build.result }}" != "success" ]]; then
            echo "❌ CI pipeline failed"
            exit 1
          fi
          echo "✅ All CI checks passed"

name: Deploy

on:
  push:
    branches: [main, dev]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deploy (skip CI check)'
        required: false
        default: false
        type: boolean

concurrency:
  group: deploy-${{ github.ref }}
  cancel-in-progress: true

jobs:
  check-ci:
    name: Check CI Status
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && !inputs.force_deploy
    steps:
      - name: Check latest CI status
        uses: actions/github-script@v7
        with:
          script: |
            const { data: runs } = await github.rest.actions.listWorkflowRunsForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              workflow_id: 'ci.yml',
              branch: context.ref.replace('refs/heads/', ''),
              per_page: 1,
              status: 'completed'
            });

            if (runs.workflow_runs.length === 0 || runs.workflow_runs[0].conclusion !== 'success') {
              core.setFailed('❌ Latest CI failed. Use force_deploy=true to override.');
            } else {
              console.log('✅ Latest CI passed - proceeding with deployment');
            }

  build:
    name: Build for Production
    runs-on: ubuntu-latest
    needs: [check-ci]
    if: always() && (needs.check-ci.result == 'success' || needs.check-ci.result == 'skipped')
    outputs:
      release-version: ${{ steps.version.outputs.release_version }}
    steps:
      - name: Setup workspace
        uses: ./.github/actions/setup-workspace
        with:
          setup-expo: true
          expo-token: ${{ secrets.EXPO_TOKEN }}

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          expo-token: ${{ secrets.EXPO_TOKEN }}
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}
          sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Generate release version
        id: version
        run: |
          RELEASE_VERSION="${GITHUB_REF#refs/heads/}-$(echo ${{ github.sha }} | cut -c1-7)"
          echo "release_version=$RELEASE_VERSION" >> $GITHUB_OUTPUT
          echo "Generated release version: $RELEASE_VERSION"

      - name: Build web application
        run: npx expo export --platform web
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: production-build
          path: dist/
          retention-days: 7

  sentry:
    name: Create Sentry Release
    runs-on: ubuntu-latest
    needs: [build]
    env:
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      SENTRY_ORG: kiflow
      SENTRY_PROJECT: react-native
    steps:
      - name: Setup workspace
        uses: ./.github/actions/setup-workspace

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: production-build
          path: dist/

      - name: Create and finalize Sentry release
        run: |
          RELEASE_VERSION="${{ needs.build.outputs.release-version }}"

          npx sentry-cli releases new $RELEASE_VERSION
          npx sentry-cli releases files $RELEASE_VERSION upload-sourcemaps dist/ --url-prefix ~/
          npx sentry-cli releases set-commits $RELEASE_VERSION --auto

          if [ "${GITHUB_REF#refs/heads/}" = "main" ]; then
            npx sentry-cli releases deploys $RELEASE_VERSION new -e production
          else
            npx sentry-cli releases deploys $RELEASE_VERSION new -e development
          fi

          npx sentry-cli releases finalize $RELEASE_VERSION

  deploy:
    name: Deploy to EAS
    runs-on: ubuntu-latest
    needs: [build, sentry]
    steps:
      - name: Setup workspace
        uses: ./.github/actions/setup-workspace
        with:
          setup-expo: true
          expo-token: ${{ secrets.EXPO_TOKEN }}

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          expo-token: ${{ secrets.EXPO_TOKEN }}
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}
          sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Deploy to EAS
        run: |
          echo "🚀 Deploying to EAS..."
          eas deploy --prod
          echo "✅ Deployment completed successfully!"

  summary:
    name: Deployment Summary
    runs-on: ubuntu-latest
    needs: [check-ci, build, sentry, deploy]
    if: always()
    steps:
      - name: Display summary
        run: |
          echo "=== DEPLOYMENT SUMMARY ==="
          echo "Trigger: ${{ github.event_name }}"
          echo "Branch: ${GITHUB_REF#refs/heads/}"
          echo "Commit: ${{ github.sha }}"

          if [[ "${{ github.event.inputs.force_deploy }}" == "true" ]]; then
            echo "⚠️  Force deployment enabled (CI check bypassed)"
          fi

          echo ""
          echo "=== JOB RESULTS ==="
          echo "CI Check: ${{ needs.check-ci.result }}"
          echo "Build: ${{ needs.build.result }}"
          echo "Sentry: ${{ needs.sentry.result }}"
          echo "Deploy: ${{ needs.deploy.result }}"

          if [[ "${{ needs.deploy.result }}" == "success" ]]; then
            echo ""
            echo "🎉 DEPLOYMENT SUCCESSFUL!"
            echo "Release: ${{ needs.build.outputs.release-version }}"
          else
            echo ""
            echo "❌ DEPLOYMENT FAILED"
            echo "Check the job logs for details"
          fi

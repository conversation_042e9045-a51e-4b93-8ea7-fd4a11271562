name: Playwright Tests
on:
  push:
    branches: [main, dev]
  workflow_dispatch:

jobs:
  test:
    name: E2E Tests
    timeout-minutes: 30
    runs-on: ubuntu-latest
    container:
      image: mcr.microsoft.com/playwright:v1.53.0-jammy
      options: --ipc=host
    env:
      PLAYWRIGHT_BASE_URL: 'http://localhost:8081'
      CI: false

    steps:
      - uses: actions/checkout@v4

      - name: Setup project
        uses: ./.github/actions/setup-project
        with:
          cache-key-suffix: -playwright
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}

      - name: Cache Playwright browsers
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: /home/<USER>/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ hashFiles('package-lock.json') }}

      - name: Install Playwright Browsers
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: npx playwright install --with-deps

      - name: Start development server
        run: |
          echo "🚀 Starting Expo development server..."
          npx expo start --web --port 8081 --clear &

          # Wait for server to be ready
          for i in {1..30}; do
            if curl -s http://localhost:8081 > /dev/null 2>&1; then
              echo "✅ Development server is ready!"
              break
            fi
            echo "Waiting for server... ($i/30)"
            sleep 2
          done

          if ! curl -s http://localhost:8081 > /dev/null 2>&1; then
            echo "❌ Server failed to start"
            exit 1
          fi

      - name: Run Playwright tests
        run: npx playwright test --reporter=html,github

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 7
          if-no-files-found: ignore

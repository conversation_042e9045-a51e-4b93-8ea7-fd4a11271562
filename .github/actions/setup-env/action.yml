name: 'Setup Environment'
description: 'Sets up project dependencies and creates environment file'
inputs:
  node-version:
    description: 'Node.js version to use'
    required: false
    default: '22.14.0'
  cache-key-suffix:
    description: 'Additional suffix for cache key'
    required: false
    default: ''
  expo-token:
    description: 'Expo authentication token'
    required: false
  gemini-api-key:
    description: 'Gemini API key for environment'
    required: false
  supabase-service-key:
    description: 'Supabase service key for environment'
    required: false
  sentry-auth-token:
    description: 'Sentry authentication token for environment'
    required: false

runs:
  using: 'composite'
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'npm'

    - name: Cache node_modules
      uses: actions/cache@v4
      id: node-modules-cache
      with:
        path: node_modules
        key: ${{ runner.os }}-node-modules-${{ hashFiles('package-lock.json') }}${{ inputs.cache-key-suffix }}
        restore-keys: |
          ${{ runner.os }}-node-modules-

    - name: Install dependencies
      if: steps.node-modules-cache.outputs.cache-hit != 'true'
      shell: bash
      run: npm ci

    - name: Setup Expo
      if: inputs.expo-token != ''
      uses: expo/expo-github-action@v8
      with:
        eas-version: latest
        token: ${{ inputs.expo-token }}

    - name: Create .env file
      shell: bash
      run: |
        touch .env
        if [ -n "${{ inputs.gemini-api-key }}" ]; then
          echo "GEMINI_API_KEY=${{ inputs.gemini-api-key }}" >> .env
        fi
        if [ -n "${{ inputs.supabase-service-key }}" ]; then
          echo "SUPABASE_SERVICE_KEY=${{ inputs.supabase-service-key }}" >> .env
        fi
        if [ -n "${{ inputs.sentry-auth-token }}" ]; then
          echo "SENTRY_AUTH_TOKEN=${{ inputs.sentry-auth-token }}" >> .env
        fi

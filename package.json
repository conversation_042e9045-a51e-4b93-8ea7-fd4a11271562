{"name": "kiflow_mobile", "main": "expo-router/entry", "version": "1.0.0", "type": "module", "scripts": {"start": "expo start", "android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "web": "DARK_MODE=media expo start --web", "test": "jest --watchAll", "lint": "expo lint", "type-check": "tsc --noEmit", "playwright-ui": "playwright test --ui", "playwright-ci": "playwright test", "db:seed": "ts-node -r tsconfig-paths/register src/scripts/seedCourses.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=chpt_96e0cfbcc84cfca"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo-google-fonts/inter": "^0.3.0", "@expo/html-elements": "^0.12.5", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.53", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.28", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/link": "^0.1.29", "@gluestack-ui/menu": "^0.2.43", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/popover": "^0.1.49", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/select": "^0.1.31", "@gluestack-ui/slider": "^0.1.32", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.29", "@gluestack-ui/textarea": "^0.1.25", "@gluestack-ui/themed": "^1.1.73", "@gluestack-ui/toast": "^1.0.9", "@gluestack-ui/tooltip": "^0.1.44", "@google/genai": "^1.0.1", "@legendapp/motion": "^2.4.0", "@mux/mux-player-react": "^3.3.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/native": "^7.0.0", "@sentry/react-native": "~6.14.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.79.0", "babel-plugin-module-resolver": "^5.0.2", "expo": "53.0.12", "expo-auth-session": "~6.1.5", "expo-av": "~15.1.4", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-file-system": "^18.0.12", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-updates": "~0.28.13", "expo-video": "~2.1.9", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "react": "^19.1.0", "react-dom": "^19.1.0", "react-native": "0.79.4", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "^2.25.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.12.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "recharts": "^2.15.3", "tailwindcss": "^3.4.17", "timers": "^0.1.1", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@chromatic-com/storybook": "^4.0.0", "@eslint/js": "^9.27.0", "@playwright/test": "^1.51.1", "@storybook/addon-a11y": "^9.0.8", "@storybook/addon-docs": "^9.0.8", "@storybook/addon-onboarding": "^9.0.8", "@storybook/addon-vitest": "^9.0.8", "@storybook/react-native-web-vite": "^9.0.8", "@types/jest": "^29.5.12", "@types/react": "^19.1.5", "@types/react-test-renderer": "^19.1.0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.25.0", "@vitest/browser": "^3.2.3", "@vitest/coverage-v8": "^3.2.3", "eslint": "^9.27.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.0.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^9.0.8", "jest": "^29.2.1", "jest-expo": "~53.0.5", "playwright": "^1.53.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react-test-renderer": "^19.1.0", "storybook": "^9.0.8", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vitest": "^3.2.3"}, "private": true}